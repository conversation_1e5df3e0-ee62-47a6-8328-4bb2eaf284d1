import os
import shutil
from datetime import datetime, timedelta

# --- CONFIGURATION ---
# The script MUST be run from the 'DAILY NOTES 2025' folder.
# It uses this path to find the list of SUs and the templates.
BASE_YEAR_PATH = os.getcwd() 
START_DATE = datetime.now()
END_DATE = datetime(2026, 12, 31) 
LOGS = []

# --- MONTH FOLDER MAPPING ---
# This dictionary helps standardize month folder names.
MONTH_MAP = {
    1: "01. Jan", 2: "02. Feb", 3: "03. March", 4: "04. April",
    5: "05. May", 6: "06. June", 7: "07. July", 8: "08. Aug",
    9: "09. Sep", 10: "10. Oct", 11: "11. Nov", 12: "12. Dec"
}

def find_template_file(su_folder_path):
    """
    Finds the most appropriate blank template file within a Service User's folder.
    It prioritizes a template in the SU's root over ones in sub-folders.
    """
    primary_template = os.path.join(su_folder_path, "BLANK TEMPLATE - DAILY NOTE.docx")
    if os.path.exists(primary_template):
        return primary_template

    # Fallback: search for variations if the primary template isn't found.
    for root, _, files in os.walk(su_folder_path):
        for file in files:
            if "BLANK TEMPLATE - DAILY NOTE" in file and file.endswith(".docx"):
                return os.path.join(root, file)
    return None

def generate_daily_notes():
    """
    Main function to orchestrate the generation of daily notes.
    It handles creating year-specific folders (e.g., 'DAILY NOTES 2026').
    """
    LOGS.append(f"Starting template generation at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    LOGS.append(f"Base Path: {BASE_YEAR_PATH}")
    LOGS.append(f"Generating files from {START_DATE.strftime('%d-%m-%Y')} to {END_DATE.strftime('%d-%m-%Y')}.\n")

    # 1. Scan for SU folders ONLY in the base year path ('DAILY NOTES 2025').
    for su_folder_name in os.listdir(BASE_YEAR_PATH):
        original_su_path = os.path.join(BASE_YEAR_PATH, su_folder_name)

        # Skip non-directory items, VOID folders, and special folders.
        if not os.path.isdir(original_su_path) or "VOID" in su_folder_name or "Previous Clients" in su_folder_name:
            continue
            
        LOGS.append(f"--- Processing Service User: {su_folder_name} ---")

        # 2. Identify the blank template from the original 2025 folder.
        template_path = find_template_file(original_su_path)
        if not template_path:
            LOGS.append(f"[ERROR] No 'BLANK TEMPLATE' found for {su_folder_name}. Skipping.\n")
            continue
        
        LOGS.append(f"Found template: {os.path.basename(template_path)}")
        files_created_count = 0
        
        # 3. Generate missing daily files for the entire date range.
        current_date = START_DATE
        while current_date <= END_DATE:
            year_str = str(current_date.year)
            
            # --- NEW LOGIC TO HANDLE YEAR FOLDERS ---
            # Determine the correct root directory for the current year.
            if "2025" in BASE_YEAR_PATH:
                current_year_root_path = BASE_YEAR_PATH.replace("2025", year_str)
            else:
                # Fallback if the script is not in a '2025' named folder
                current_year_root_path = os.path.join(os.path.dirname(BASE_YEAR_PATH), f"DAILY NOTES {year_str}")

            # Define the SU folder path for the current year (e.g., ...\DAILY NOTES 2026\1. Jadon)
            target_su_path = os.path.join(current_year_root_path, su_folder_name)
            
            # Create the main year folder and the SU folder inside it if they don't exist
            os.makedirs(target_su_path, exist_ok=True)
            
            # 4. Organize files by month inside the correct year/SU folder.
            month_folder_name = MONTH_MAP.get(current_date.month)
            target_month_path = os.path.join(target_su_path, month_folder_name)
            
            if not os.path.exists(target_month_path):
                os.makedirs(target_month_path)
                LOGS.append(f"Created folder: {target_month_path}")

            # Define the final target file path.
            file_name = f"{current_date.strftime('%d.%m.%y')}.docx"
            target_file_path = os.path.join(target_month_path, file_name)

            # Skip creating the file if it already exists.
            if not os.path.exists(target_file_path):
                try:
                    shutil.copy(template_path, target_file_path)
                    files_created_count += 1
                except Exception as e:
                    LOGS.append(f"[ERROR] Could not create file {target_file_path}. Reason: {e}")
            
            current_date += timedelta(days=1)
        
        LOGS.append(f"Summary for {su_folder_name}: {files_created_count} new files created.\n")

    # 5. Provide a final report.
    print("\n--- Daily Template Generation Report ---")
    for log_entry in LOGS:
        print(log_entry)
    
    report_file_path = os.path.join(BASE_YEAR_PATH, "template_generation_report.txt")
    with open(report_file_path, "w") as f:
        f.write("\n".join(LOGS))
    print(f"\n--- A detailed report has been saved to: {report_file_path} ---")

if __name__ == "__main__":
    generate_daily_notes()